import { Modu<PERSON> } from '@nestjs/common';

import { LinksModule } from './links/links.module';

import { AppService } from './app.service';
import { AppController } from './app.controller';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from './database/database.module';
import { UsersModule } from './users/users.module';
import { auth } from '@repo/database/server';
import { AuthModule } from '@thallesp/nestjs-better-auth';

@Module({
  imports: [
    LinksModule,
    UsersModule,
    ConfigModule.forRoot(),
    DatabaseModule,
    AuthModule.forRoot(auth),
    UsersModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
