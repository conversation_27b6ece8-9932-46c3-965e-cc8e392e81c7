{"name": "api", "version": "0.0.0", "private": true, "scripts": {"dev": "nest start --watch", "build": "nest build", "start": "nest start", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "test": "jest", "test:watch": "jest --watch", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\""}, "dependencies": {"@nestjs/common": "^11.0.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.0", "@nestjs/mapped-types": "*", "@nestjs/platform-express": "^11.0.0", "@repo/api": "workspace:*", "@repo/database": "workspace:*", "drizzle-orm": "^0.44.5", "pg": "^8.16.3", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1"}, "devDependencies": {"@jest/globals": "^29.7.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.0", "@repo/eslint-config": "workspace:*", "@repo/jest-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/express": "^4.17.17", "@types/node": "^22.10.7", "@types/pg": "^8.15.5", "@types/supertest": "^6.0.0", "drizzle-kit": "^0.31.4", "jest": "^29.7.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.4.3", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "5.5.4"}}