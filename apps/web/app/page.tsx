'use client';

import { useAuth } from '../lib/auth-context';

export default function Home() {
  const {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    signInWithGoogle,
    signInWithGithub,
  } = useAuth();

  return (
    <div
      style={{
        padding: '40px',
        maxWidth: '500px',
        margin: '0 auto',
        fontFamily: 'system-ui, sans-serif',
        backgroundColor: 'var(--background)',
        color: 'var(--foreground)',
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
      }}
    >
      <h1 style={{ textAlign: 'center', marginBottom: '30px' }}>Looma.ai</h1>

      {loading && (
        <div style={{ textAlign: 'center' }}>
          <p>Loading...</p>
        </div>
      )}

      {user ? (
        <div style={{ textAlign: 'center' }}>
          <h2 style={{ color: '#28a745', marginBottom: '20px' }}>
            ✅ Welcome, {user.displayName || user.email}!
          </h2>
          <div style={{ marginBottom: '20px' }}>
            <p>
              <strong>Email:</strong> {user.email}
            </p>
            <p>
              <strong>UID:</strong> {user.uid}
            </p>
          </div>

          <button
            onClick={signOut}
            style={{
              padding: '12px 24px',
              margin: '10px',
              backgroundColor: '#dc3545',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '16px',
              display: 'block',
              width: '100%',
            }}
          >
            🚪 Sign Out
          </button>

          <button
            onClick={async () => {
              try {
                const token = await user.getIdToken();
                const response = await fetch('/api/users/me', {
                  headers: {
                    Authorization: `Bearer ${token}`,
                  },
                });
                const data = await response.json();
                console.log('✅ Backend response:', data);
              } catch (error) {
                console.error('❌ Backend error:', error);
              }
            }}
            style={{
              padding: '12px 24px',
              margin: '10px',
              backgroundColor: '#17a2b8',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '16px',
              display: 'block',
              width: '100%',
            }}
          >
            🔗 Test Authenticated Backend Call
          </button>
        </div>
      ) : (
        <div style={{ textAlign: 'center' }}>
          <h2 style={{ color: '#dc3545', marginBottom: '20px' }}>
            🔐 Please Sign In
          </h2>

          <div style={{ marginBottom: '20px' }}>
            <button
              onClick={() => signInWithGoogle()}
              style={{
                padding: '12px 24px',
                margin: '10px',
                backgroundColor: '#db4437',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '16px',
                display: 'block',
                width: '100%',
              }}
            >
              🔑 Sign In with Google
            </button>

            <button
              onClick={() => signInWithGithub()}
              style={{
                padding: '12px 24px',
                margin: '10px',
                backgroundColor: '#333',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '16px',
                display: 'block',
                width: '100%',
              }}
            >
              🔑 Sign In with GitHub
            </button>

            <button
              onClick={() => signIn('<EMAIL>', 'password123')}
              style={{
                padding: '12px 24px',
                margin: '10px',
                backgroundColor: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '16px',
                display: 'block',
                width: '100%',
              }}
            >
              🔑 Test Email Sign In
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
