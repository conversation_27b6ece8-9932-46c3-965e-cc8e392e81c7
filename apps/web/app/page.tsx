'use client';

import { authClient } from '../lib/auth-client';

export default function Home() {
  const { data: session, isPending: isLoading } = authClient.useSession();
  console.log({ session });

  // Auth test interface
  return (
    <div
      style={{
        padding: '40px',
        maxWidth: '500px',
        margin: '0 auto',
        fontFamily: 'system-ui, sans-serif',
        backgroundColor: 'var(--background)',
        color: 'var(--foreground)',
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
      }}
    >
      <h1 style={{ textAlign: 'center', marginBottom: '30px' }}>Auth Test</h1>

      {isLoading && (
        <div style={{ textAlign: 'center' }}>
          <p>Loading session...</p>
        </div>
      )}

      {session && (
        <div style={{ textAlign: 'center' }}>
          <h2>✅ Logged in successfully!</h2>
          <p>
            <strong>Name:</strong>{' '}
            {(session as any)?.user?.name || 'Unknown User'}
          </p>
          <p>
            <strong>Email:</strong>{' '}
            {(session as any)?.user?.email || 'No email'}
          </p>
          <button
            onClick={async () => {
              try {
                await authClient.signOut();
                console.log('Sign out successful');
              } catch (error) {
                console.error('Sign out error:', error);
              }
            }}
            style={{
              padding: '12px 24px',
              margin: '20px auto',
              backgroundColor: '#dc3545',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '16px',
              display: 'block',
            }}
          >
            Sign Out
          </button>
        </div>
      )}

      {!session && !isLoading && (
        <div style={{ textAlign: 'center' }}>
          <h2>🔐 Not logged in</h2>
          <div style={{ marginTop: '30px' }}>
            <button
              onClick={() => {
                console.log('=== AUTH CLIENT DEBUG ===');
                console.log('Auth client methods:', Object.keys(authClient));
                console.log('Auth client object:', authClient);
                console.log('Auth client type:', typeof authClient);
              }}
              style={{
                padding: '12px 24px',
                margin: '10px',
                backgroundColor: '#6c757d',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '16px',
                display: 'block',
                width: '100%',
              }}
            >
              🔍 Debug Auth Client
            </button>

            <button
              onClick={async () => {
                console.log('=== TESTING BACKEND CONNECTION ===');
                try {
                  const response = await fetch('http://localhost:3001/');
                  const text = await response.text();
                  console.log('✅ Backend response:', text);

                  // Test auth endpoint
                  const authResponse = await fetch(
                    'http://localhost:3001/api/auth',
                  );
                  console.log('Auth endpoint status:', authResponse.status);
                  const authText = await authResponse.text();
                  console.log('Auth endpoint response:', authText);
                } catch (error) {
                  console.error('❌ Backend connection error:', error);
                }
              }}
              style={{
                padding: '12px 24px',
                margin: '10px',
                backgroundColor: '#17a2b8',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '16px',
                display: 'block',
                width: '100%',
              }}
            >
              🔗 Test Backend Connection
            </button>

            <button
              onClick={async () => {
                console.log('=== ATTEMPTING SIGN UP ===');
                try {
                  const result = await (authClient as any).signUp.email({
                    email: '<EMAIL>',
                    password: 'password123',
                    name: 'Test User',
                    username: 'testuser',
                  });
                  console.log('✅ Sign up result:', result);
                } catch (error) {
                  console.error('❌ Sign up error:', error);
                }
              }}
              style={{
                padding: '12px 24px',
                margin: '10px',
                backgroundColor: '#0070f3',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '16px',
                display: 'block',
                width: '100%',
              }}
            >
              📝 Sign Up Test User
            </button>

            <button
              onClick={async () => {
                console.log('=== ATTEMPTING SIGN IN ===');
                try {
                  const result = await (authClient as any).signIn.email({
                    email: '<EMAIL>',
                    password: 'password123',
                  });
                  console.log('✅ Sign in result:', result);
                } catch (error) {
                  console.error('❌ Sign in error:', error);
                }
              }}
              style={{
                padding: '12px 24px',
                margin: '10px',
                backgroundColor: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '16px',
                display: 'block',
                width: '100%',
              }}
            >
              🔑 Sign In Test User
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
