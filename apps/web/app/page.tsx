'use client';

export default function Home() {
  // TODO: Implement Firebase authentication

  return (
    <div
      style={{
        padding: '40px',
        maxWidth: '500px',
        margin: '0 auto',
        fontFamily: 'system-ui, sans-serif',
        backgroundColor: 'var(--background)',
        color: 'var(--foreground)',
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
      }}
    >
      <h1 style={{ textAlign: 'center', marginBottom: '30px' }}>Looma.ai</h1>

      <div style={{ textAlign: 'center' }}>
        <p style={{ marginBottom: '20px' }}>
          Ready for Firebase authentication integration
        </p>

        <button
          onClick={async () => {
            console.log('=== TESTING BACKEND CONNECTION ===');
            try {
              const response = await fetch('/api/users/me');
              const data = await response.json();
              console.log('✅ Backend response:', data);
            } catch (error) {
              console.error('❌ Backend error:', error);
            }
          }}
          style={{
            padding: '12px 24px',
            margin: '10px',
            backgroundColor: '#17a2b8',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '16px',
            display: 'block',
            width: '100%',
          }}
        >
          🔗 Test Backend Connection
        </button>
      </div>
    </div>
  );
}
