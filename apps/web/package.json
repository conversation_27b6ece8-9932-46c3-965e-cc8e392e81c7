{"name": "web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack --port 3001", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@repo/database": "workspace:*", "@repo/ui": "workspace:*", "auth": "^1.2.3", "better": "^0.1.0", "better-auth": "^1.3.7", "next": "^15.4.2", "react": "^19.1.0", "react-dom": "^19.1.0", "zod": "^4.1.5"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4.1.12", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "eslint": "^9.31.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.12", "typescript": "5.8.2"}}